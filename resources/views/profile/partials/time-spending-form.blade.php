<!-- Compact Time Spending Header -->
<div class="text-center mb-4">
    <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-green-600 to-emerald-600 rounded-lg mb-2 shadow-sm">
        <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
        </svg>
    </div>
    <h2 class="text-lg font-bold text-gray-900 mb-1">Time Spending Service</h2>
    <p class="text-gray-600 text-xs max-w-md mx-auto">Configure your hourly rate, location, and availability schedule</p>
</div>

<!-- Information Notice -->
@if(!$user->is_time_spending_enabled)
    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
        <div class="flex items-start">
            <svg class="w-5 h-5 text-blue-600 mr-3 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
            </svg>
            <div>
                <h3 class="text-base font-semibold text-blue-900 mb-1">Enable Time Spending Service</h3>
                <p class="text-xs text-blue-800 mb-2">
                    To activate your Time Spending service, please go to the "Settings" tab and enable "Show Time Spending Service".
                </p>
                <p class="text-xs text-blue-800">
                    You can set your hourly rate here, but the service will only be visible to others when enabled in Settings.
                </p>
            </div>
        </div>
    </div>
@endif

<div id="time-spending-content" class="bg-white rounded-xl border border-gray-200 shadow-sm">
    <div class="space-y-4 p-4">
        <form method="post" action="{{ route('profile.time-spending.update') }}" class="space-y-4" onsubmit="return validateLocationSelection()">
            @csrf
            @method('patch')
            <input type="hidden" name="current_tab" value="time-spending">

            <!-- Current Status Summary -->
            @php
                $statusBgClass = $user->hasActiveTimeSpendingSubscription()
                    ? 'bg-gradient-to-r from-green-50 to-emerald-50 border-green-200'
                    : ($user->hasExpiredTimeSpendingSubscription()
                        ? 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200'
                        : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200');
            @endphp
            <div class="{{ $statusBgClass }} rounded-lg p-4 border">
                @php
                    $iconColor = $user->hasActiveTimeSpendingSubscription()
                        ? 'text-green-600'
                        : ($user->hasExpiredTimeSpendingSubscription()
                            ? 'text-red-600'
                            : 'text-blue-600');
                @endphp
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    @if($user->hasActiveTimeSpendingSubscription())
                        <svg class="w-4 h-4 mr-2 {{ $iconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    @elseif($user->hasExpiredTimeSpendingSubscription())
                        <svg class="w-4 h-4 mr-2 {{ $iconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    @else
                        <svg class="w-4 h-4 mr-2 {{ $iconColor }}" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    @endif
                    Current Status
                    @if($user->is_time_spending_enabled)
                        @if($user->hasActiveTimeSpendingSubscription())
                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Active
                            </span>
                        @elseif($user->hasExpiredTimeSpendingSubscription())
                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                                </svg>
                                Expired
                            </span>
                            @if(\App\Models\Feature::isSubscriptionModelEnabled())
                                <button type="button" class="ml-2 px-2 py-1 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-all duration-200" onclick="showInlineSubscriptionPlans()">
                                    Renew Plan
                                </button>
                            @endif
                        @elseif(\App\Models\Feature::isSubscriptionModelEnabled())
                            <button type="button" class="ml-2 px-2 py-1 bg-red-600 text-white text-xs font-medium rounded-lg hover:bg-red-700 transition-all duration-200" onclick="showInlineSubscriptionPlans()">
                                Subscription Required
                            </button>
                        @else
                            <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                Active
                            </span>
                        @endif
                    @else
                        <span class="ml-2 inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800">
                            <svg class="w-3 h-3 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                            Inactive
                        </span>
                    @endif
                </h4>

                <div class="grid grid-cols-1 sm:grid-cols-3 gap-3">
                    <div class="bg-white rounded-lg p-3 border border-gray-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-600">Hourly Rate</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    @if($user->hourly_rate)
                                        ₹{{ number_format($user->hourly_rate, 2) }}
                                    @else
                                        Not Set
                                    @endif
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-3 border border-gray-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-600">Location</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    {{ Str::limit($user->service_location ?? 'Not Set', 15) }}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div class="bg-white rounded-lg p-3 border border-gray-200">
                        <div class="flex items-center">
                            <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div>
                                <p class="text-xs text-gray-600">Status</p>
                                <p class="text-sm font-semibold text-gray-900">
                                    {{ $user->is_time_spending_enabled ? 'Available' : 'Not Available' }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>

                @if($user->hasActiveTimeSpendingSubscription())
                    @php
                        $queuedSubscription = $user->queuedSubscription;
                        $finalExpiryDate = $queuedSubscription ? $queuedSubscription->expires_at : $user->time_spending_subscription_expires_at;
                    @endphp
                    <div class="mt-3 pt-3 border-t border-green-200">
                        <p class="text-xs text-green-700">
                            <strong>Subscription expires:</strong> {{ $finalExpiryDate->format('M d, Y') }}
                        </p>
                    </div>
                @endif
            </div>

            <!-- Display validation errors -->
            @if ($errors->any())
                <div class="mb-4 bg-red-50 border border-red-200 rounded-lg p-3">
                    <div class="flex">
                        <svg class="w-4 h-4 text-red-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path>
                        </svg>
                        <div class="flex-1">
                            <h4 class="text-xs font-medium text-red-800 mb-1">Please fix the following errors:</h4>
                            <ul class="text-xs text-red-700 space-y-1">
                                @foreach ($errors->all() as $error)
                                    <li>• {{ $error }}</li>
                                @endforeach
                            </ul>
                        </div>
                    </div>
                </div>
            @endif
            <!-- Hourly Rate Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                    </svg>
                    Hourly Rate
                </h4>
                <div>
                    <label for="hourly_rate" class="block text-sm font-medium text-gray-700 mb-2">
                        Rate (INR)
                    </label>
                    <div class="relative">
                        <input id="hourly_rate"
                               name="hourly_rate"
                               type="number"
                               step="0.01"
                               min="0"
                               max="999999.99"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm"
                               style="padding-left: 30px;"
                               value="{{ old('hourly_rate', $user->hourly_rate) }}"
                               placeholder="0.00"
                               required
                               oninput="updateEarningsBreakdown()">
                        <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none pl-3">
                            <span class="text-gray-500 text-sm font-medium">₹</span>
                        </div>
                    </div>
                    <p class="mt-1 text-xs text-gray-500">Set your hourly rate in Indian Rupees (INR)</p>
                    <x-input-error class="mt-2" :messages="$errors->get('hourly_rate')" />

                    <!-- Simple Earnings Breakdown -->
                    @php
                        $commissionPercentage = \App\Models\Setting::get('commission_percentage', 10);
                        $shouldShowBreakdown = $user->hourly_rate && $commissionPercentage > 0;
                    @endphp
                    <div id="earnings-breakdown" class="mt-3 p-3 bg-gray-50 rounded-lg" style="{{ $shouldShowBreakdown ? '' : 'display: none;' }}">
                        <div class="text-xs space-y-1">
                            <div class="flex justify-between">
                                <span class="text-gray-600">Your hourly rate:</span>
                                <span class="font-medium text-gray-900" id="display-hourly-rate">₹{{ number_format($user->hourly_rate ?? 0, 2) }}</span>
                            </div>
                            <div class="flex justify-between">
                                <span class="text-gray-600">Our commission (<span id="commission-percentage">{{ $commissionPercentage }}</span>%):</span>
                                <span class="font-medium text-red-600" id="commission-amount">-₹{{ number_format(($user->hourly_rate ?? 0) * ($commissionPercentage / 100), 2) }}</span>
                            </div>
                            <div class="border-t border-gray-300 pt-1 mt-1">
                                <div class="flex justify-between">
                                    <span class="text-gray-700 font-medium">You will receive:</span>
                                    <span class="font-semibold text-green-600" id="net-earnings">₹{{ number_format(($user->hourly_rate ?? 0) - (($user->hourly_rate ?? 0) * ($commissionPercentage / 100)), 2) }}</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Service Location Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Service Location
                </h4>
                <div>
                    <label for="service_location" class="block text-sm font-medium text-gray-700 mb-2">
                        Location
                    </label>
                    <div class="relative">
                        <input id="service_location"
                               name="service_location"
                               type="text"
                               class="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 text-sm location-input"
                               style="padding-left: 35px;"
                               value="{{ old('service_location', $user->service_location) }}"
                               placeholder="Click here to search and select location..."
                               autocomplete="off"
                               readonly
                               required>
                        <input type="hidden" id="location_selected" name="location_selected" value="{{ old('location_selected', $user->service_location ? '1' : '0') }}">
                        <div class="absolute inset-y-0 left-0 flex items-center pointer-events-none pl-3">
                            <svg class="w-4 h-4 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                        </div>

                        <!-- Selection Status Indicator -->
                        <div id="location-status-indicator" class="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none hidden">
                            <svg class="w-4 h-4 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                        </div>

                        <!-- Location Autocomplete Dropdown -->
                        <div id="locationDropdown" class="absolute top-full left-0 right-0 bg-white border border-gray-300 rounded-lg shadow-lg z-50 max-h-60 overflow-y-auto hidden mt-1">
                            <div id="locationSuggestions" class="py-1">
                                <!-- Suggestions will be populated here -->
                            </div>
                            <div id="locationLoading" class="hidden p-3 text-center text-gray-500">
                                <svg class="w-4 h-4 animate-spin mx-auto mb-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                </svg>
                                <span class="text-xs">Searching...</span>
                            </div>
                            <div id="locationNoResults" class="hidden p-3 text-center text-gray-500">
                                <span class="text-xs">No locations found</span>
                            </div>
                        </div>
                    </div>

                    <!-- Error Message Container -->
                    <div id="location-error-container" class="mt-2"></div>

                    <p class="mt-1 text-xs text-gray-500">Specify the city/area where you provide your services</p>
                    <x-input-error class="mt-2" :messages="$errors->get('service_location')" />
                </div>
            </div>

            <!-- Availability Schedule Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <h4 class="text-base font-semibold text-gray-900 mb-3 flex items-center">
                    <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Weekly Availability
                </h4>

                <div class="bg-gray-50 rounded-lg border border-gray-200 overflow-hidden">
                    @php
                        $days = [
                            'monday' => 'Mon',
                            'tuesday' => 'Tue',
                            'wednesday' => 'Wed',
                            'thursday' => 'Thu',
                            'friday' => 'Fri',
                            'saturday' => 'Sat',
                            'sunday' => 'Sun'
                        ];
                        $schedule = old('availability_schedule', $user->availability_schedule ?? []);
                    @endphp

                    @foreach($days as $dayKey => $dayName)
                        @php
                            $daySchedule = $schedule[$dayKey] ?? ['start_time' => '09:00', 'end_time' => '17:00', 'is_holiday' => false];
                            $isHoliday = $daySchedule['is_holiday'] ?? false;
                        @endphp
                        <div class="flex items-center justify-between p-3 border-b border-gray-200 last:border-b-0 hover:bg-gray-100 transition-colors availability-row {{ $isHoliday ? 'bg-red-50' : '' }}" id="day-row-{{ $dayKey }}">
                            <!-- Day Name -->
                            <div class="w-12">
                                <span class="text-sm font-medium text-gray-900">{{ $dayName }}</span>
                            </div>

                            <!-- Time Inputs -->
                            <div class="flex items-center space-x-2 time-section" id="time-inputs-{{ $dayKey }}" style="{{ $isHoliday ? 'display: none;' : 'display: flex;' }}">
                                <input type="time"
                                       id="start_time_{{ $dayKey }}"
                                       name="availability_schedule[{{ $dayKey }}][start_time]"
                                       value="{{ $daySchedule['start_time'] ?? '09:00' }}"
                                       class="px-2 py-1 border border-gray-300 rounded-md text-xs focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                       data-day="{{ $dayKey }}"
                                       data-type="start"
                                       required>

                                <span class="text-xs text-gray-500">to</span>

                                <input type="time"
                                       id="end_time_{{ $dayKey }}"
                                       name="availability_schedule[{{ $dayKey }}][end_time]"
                                       value="{{ $daySchedule['end_time'] ?? '17:00' }}"
                                       class="px-2 py-1 border border-gray-300 rounded-md text-xs focus:ring-2 focus:ring-green-500 focus:border-green-500"
                                       data-day="{{ $dayKey }}"
                                       data-type="end"
                                       required>
                            </div>

                            <!-- Holiday Message -->
                            <div class="flex items-center text-red-600" id="holiday-message-{{ $dayKey }}" style="{{ $isHoliday ? 'display: flex;' : 'display: none;' }}">
                                <span class="text-xs font-medium">Not Available</span>
                            </div>

                            <!-- Holiday Toggle -->
                            <div class="flex items-center">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox"
                                           name="availability_schedule[{{ $dayKey }}][is_holiday]"
                                           value="1"
                                           {{ $isHoliday ? 'checked' : '' }}
                                           class="sr-only peer holiday-toggle"
                                           data-day="{{ $dayKey }}">
                                    <div class="relative w-8 h-4 bg-gray-200 peer-focus:outline-none peer-focus:ring-2 peer-focus:ring-red-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-3 after:w-3 after:transition-all peer-checked:bg-red-500"></div>
                                    <span class="ml-2 text-xs text-gray-600">Off</span>
                                </label>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Save Button Section -->
            <div class="bg-white rounded-lg p-4 border border-gray-200">
                <div class="flex items-center justify-end">
                    <button type="submit" class="px-4 py-2 bg-green-600 text-white font-medium rounded-lg hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 transition-all duration-200 shadow-sm text-sm">
                        Save Time Spending Settings
                    </button>
                </div>
            </div>

            <!-- Success Message -->
            @if (session('status') === 'time-spending-updated')
                <div class="p-3 bg-green-50 border border-green-200 rounded-lg">
                    <div class="flex items-center">
                        <svg class="w-4 h-4 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                        <p class="text-xs text-green-800">Time spending settings updated successfully!</p>
                    </div>
                </div>
            @endif
        </div>
    </form>
</div>



<style>
/* Update Plan Button Gradient */
.btn-gradient-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    color: white;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 12px;
}

.btn-gradient-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    color: white;
    transform: translateY(-1px);
}

/* Compact availability row styling */
.availability-row {
    transition: all 0.2s ease-in-out;
}

.availability-row:hover {
    background-color: rgba(243, 244, 246, 0.8);
}

/* Holiday row styling */
.availability-row.holiday-active {
    background-color: rgba(254, 242, 242, 0.8) !important;
}

.availability-row.holiday-active:hover {
    background-color: rgba(254, 242, 242, 1) !important;
}

.location-suggestion {
    transition: background-color 0.15s ease-in-out;
}

.location-suggestion:hover {
    background-color: #f3f4f6;
}

.location-suggestion.selected {
    background-color: #dbeafe;
}

.location-input[readonly] {
    background-color: #f9fafb;
    cursor: pointer;
}

.location-input[readonly]:hover {
    background-color: #f3f4f6;
}

.location-input:not([readonly]) {
    background-color: white;
}

.location-validation-error {
    border-color: #ef4444 !important;
    box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1) !important;
}

/* Compact time input field styling */
.time-input-field {
    min-width: 90px !important;
    width: 90px !important;
    font-family: 'Inter', 'Segoe UI', system-ui, sans-serif;
    font-variant-numeric: tabular-nums;
    font-size: 12px !important;
    font-weight: 500;
    text-align: center;
    padding: 4px 8px !important;
    border: 1px solid #d1d5db !important;
    border-radius: 6px !important;
    background-color: #ffffff !important;
    transition: all 0.2s ease-in-out;
}

.time-input-field:focus {
    border-color: #10b981 !important;
    box-shadow: 0 0 0 2px rgba(16, 185, 129, 0.1) !important;
    outline: none !important;
}

.time-input-field:hover {
    border-color: #9ca3af !important;
}

/* Global compact time input styling */
input[type="time"] {
    min-width: 90px !important;
    font-size: 12px !important;
    font-weight: 500;
    text-align: center;
    -webkit-appearance: none;
    -moz-appearance: textfield;
}

/* Better time input appearance for webkit browsers */
input[type="time"]::-webkit-calendar-picker-indicator {
    background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%23374151" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12,6 12,12 16,14"></polyline></svg>') no-repeat center;
    background-size: 16px 16px;
    cursor: pointer;
    opacity: 0.7;
    width: 20px;
    height: 20px;
    position: absolute;
    right: 8px;
    top: 50%;
    transform: translateY(-50%);
}

input[type="time"]::-webkit-calendar-picker-indicator:hover {
    opacity: 1;
}

/* Firefox time input styling */
input[type="time"]::-moz-focus-inner {
    border: 0;
}

/* Remove default styling for better cross-browser compatibility */
input[type="time"]::-webkit-inner-spin-button,
input[type="time"]::-webkit-outer-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/* Compact time section styling */
.time-section {
    min-width: 200px;
}

/* Responsive design */
@media (max-width: 768px) {
    .availability-row {
        flex-direction: column;
        align-items: flex-start !important;
        gap: 8px;
        padding: 12px 16px;
    }

    .time-section {
        min-width: auto;
        width: 100%;
    }

    .time-input-field {
        min-width: 80px !important;
        width: 80px !important;
        font-size: 14px !important; /* Prevent zoom on iOS */
    }
}

/* Additional mobile improvements */
@media (max-width: 480px) {
    .time-input-field {
        min-width: 110px !important;
        width: 110px !important;
        font-size: 16px !important;
        padding: 6px 8px !important;
    }

    .time-section {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .time-section .flex {
        width: 100%;
        justify-content: space-between;
    }
}
</style>

<script>
// Global validation function for form onsubmit
function validateLocationSelection() {
    const locationInput = document.getElementById('service_location');
    const locationSelected = document.getElementById('location_selected');

    console.log('Global validation - Location selected value:', locationSelected.value);
    console.log('Global validation - Location input value:', locationInput.value);

    if (!locationInput.value || locationSelected.value !== '1') {
        // Add visual error styling
        locationInput.classList.add('location-validation-error');

        // Show error message in proper container
        const errorContainer = document.getElementById('location-error-container');
        let errorDiv = document.getElementById('location-error-message');
        if (!errorDiv) {
            errorDiv = document.createElement('div');
            errorDiv.id = 'location-error-message';
            errorDiv.className = 'text-sm text-red-600 font-medium bg-red-50 border border-red-200 rounded-lg p-3';
            errorContainer.appendChild(errorDiv);
        }
        errorDiv.textContent = 'Please select a location from the dropdown suggestions. Manual typing is not allowed.';

        // Show alert as well
        alert('Please select a location from the dropdown suggestions. Manual typing is not allowed.');

        // Clear the invalid input
        locationInput.value = '';
        locationSelected.value = '0';

        // Scroll to the field and focus
        locationInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
        setTimeout(() => {
            if (locationInput.hasAttribute('readonly')) {
                locationInput.removeAttribute('readonly');
                locationInput.placeholder = 'Start typing your service location...';
            }
            locationInput.focus();
        }, 500);

        return false; // Prevent form submission
    }

    return true; // Allow form submission
}

// Initialize time input functionality
function initializeTimeInputs() {
    const timeInputs = document.querySelectorAll('input[type="time"]');

    timeInputs.forEach(input => {
        // Add click event to ensure time picker opens
        input.addEventListener('click', function() {
            this.showPicker && this.showPicker();
        });

        // Add focus event for better accessibility
        input.addEventListener('focus', function() {
            this.select();
        });

        // Add validation for time range
        input.addEventListener('change', function() {
            validateTimeRange(this);
        });

        // Add better keyboard support
        input.addEventListener('keydown', function(e) {
            // Allow arrow keys to increment/decrement time
            if (e.key === 'ArrowUp' || e.key === 'ArrowDown') {
                e.preventDefault();
                adjustTime(this, e.key === 'ArrowUp' ? 1 : -1);
            }
        });
    });
}

// Validate time range (start time should be before end time)
function validateTimeRange(input) {
    const day = input.dataset.day;
    const type = input.dataset.type;

    if (!day || !type) return;

    const startTimeInput = document.querySelector(`input[data-day="${day}"][data-type="start"]`);
    const endTimeInput = document.querySelector(`input[data-day="${day}"][data-type="end"]`);

    if (startTimeInput && endTimeInput && startTimeInput.value && endTimeInput.value) {
        // Validate time format first
        if (!isValidTimeFormat(startTimeInput.value) || !isValidTimeFormat(endTimeInput.value)) {
            showTimeError(input, 'Please enter a valid time format (HH:MM)');
            return false;
        }

        // Validate 15-minute increments
        if (!isValidTimeIncrement(startTimeInput.value) || !isValidTimeIncrement(endTimeInput.value)) {
            showTimeError(input, 'Time must be in 15-minute increments (e.g., 09:00, 09:15, 09:30, 09:45)');

            // Auto-correct to nearest 15-minute increment
            setTimeout(() => {
                if (type === 'start') {
                    startTimeInput.value = roundToNearestQuarter(startTimeInput.value);
                } else {
                    endTimeInput.value = roundToNearestQuarter(endTimeInput.value);
                }
                validateTimeRange(input);
            }, 1500);
            return false;
        }

        const startTime = new Date(`2000-01-01 ${startTimeInput.value}`);
        const endTime = new Date(`2000-01-01 ${endTimeInput.value}`);

        // Check if end time is after start time
        if (startTime >= endTime) {
            showTimeError(input, 'End time must be at least 15 minutes after start time');

            // Auto-correct the time
            setTimeout(() => {
                if (type === 'start') {
                    // If start time is too late, set it to 1 hour before end time
                    const newStartTime = new Date(endTime.getTime() - 60 * 60 * 1000);
                    startTimeInput.value = newStartTime.toTimeString().slice(0, 5);
                } else {
                    // If end time is too early, set it to 1 hour after start time
                    const newEndTime = new Date(startTime.getTime() + 60 * 60 * 1000);
                    endTimeInput.value = newEndTime.toTimeString().slice(0, 5);
                }
                clearTimeError(input);
            }, 2000);
            return false;
        } else {
            clearTimeError(startTimeInput);
            clearTimeError(endTimeInput);
            return true;
        }
    }
    return true;
}

// Check if time is in valid format (HH:MM)
function isValidTimeFormat(timeString) {
    const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timeRegex.test(timeString);
}

// Check if time is in 15-minute increments
function isValidTimeIncrement(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    return minutes % 15 === 0;
}

// Round time to nearest 15-minute increment
function roundToNearestQuarter(timeString) {
    const [hours, minutes] = timeString.split(':').map(Number);
    const roundedMinutes = Math.round(minutes / 15) * 15;

    let newHours = hours;
    let newMinutes = roundedMinutes;

    if (newMinutes >= 60) {
        newMinutes = 0;
        newHours++;
    }

    if (newHours >= 24) {
        newHours = 23;
        newMinutes = 45;
    }

    return `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
}

// Show time validation error
function showTimeError(input, message) {
    // Remove existing error
    clearTimeError(input);

    const errorDiv = document.createElement('div');
    errorDiv.className = 'time-error text-xs text-red-600 mt-1';
    errorDiv.textContent = message;

    input.parentNode.appendChild(errorDiv);
}

// Clear time validation error
function clearTimeError(input) {
    const existingError = input.parentNode.querySelector('.time-error');
    if (existingError) {
        existingError.remove();
    }

    // Reset styling
    input.style.borderColor = '';
    input.style.boxShadow = '';
}

// Adjust time with arrow keys
function adjustTime(input, direction) {
    if (!input.value) {
        input.value = '09:00';
        return;
    }

    const [hours, minutes] = input.value.split(':').map(Number);
    let newMinutes = minutes + (direction * 15); // 15-minute increments
    let newHours = hours;

    if (newMinutes >= 60) {
        newMinutes = 0;
        newHours++;
    } else if (newMinutes < 0) {
        newMinutes = 45;
        newHours--;
    }

    if (newHours >= 24) newHours = 0;
    if (newHours < 0) newHours = 23;

    const formattedTime = `${String(newHours).padStart(2, '0')}:${String(newMinutes).padStart(2, '0')}`;
    input.value = formattedTime;

    // Trigger validation
    validateTimeRange(input);
}

// Initialize time input functionality
function initializeTimeInputs() {
    // Add event listeners for time inputs
    document.querySelectorAll('.time-input-field').forEach(input => {
        input.addEventListener('change', function() {
            validateTimeRange(this);
        });

        input.addEventListener('blur', function() {
            validateTimeRange(this);
        });

        // Real-time validation as user types
        input.addEventListener('input', function() {
            clearTimeout(this.validationTimeout);
            this.validationTimeout = setTimeout(() => {
                validateTimeRange(this);
            }, 500);
        });

        // Prevent invalid characters and format
        input.addEventListener('keypress', function(e) {
            const char = String.fromCharCode(e.which);
            const currentValue = this.value;

            // Allow numbers, colon, backspace, delete, arrow keys
            if (!/[0-9:]/.test(char) && ![8, 9, 37, 38, 39, 40, 46].includes(e.keyCode)) {
                e.preventDefault();
            }

            // Prevent more than 5 characters (HH:MM)
            if (currentValue.length >= 5 && ![8, 9, 37, 38, 39, 40, 46].includes(e.keyCode)) {
                e.preventDefault();
            }
        });

        // Auto-format time input
        input.addEventListener('input', function() {
            let value = this.value.replace(/[^0-9]/g, '');

            if (value.length >= 2) {
                value = value.substring(0, 2) + ':' + value.substring(2, 4);
            }

            this.value = value;
        });
    });
}

// Show/Hide Subscription Plans Functions
function showInlineSubscriptionPlans() {
    console.log('Showing inline subscription plans...');

    // Ensure payment modal is hidden
    const paymentModal = document.getElementById('paymentModal');
    if (paymentModal) {
        paymentModal.style.display = 'none';
        paymentModal.classList.add('d-none');
        // Also hide any Bootstrap modal instance
        if (typeof bootstrap !== 'undefined') {
            const modal = bootstrap.Modal.getInstance(paymentModal);
            if (modal) {
                modal.hide();
            }
        }
        console.log('Hidden payment modal');
    }

    // Hide the main time spending form (the one with ID time-spending-content)
    const timeSpendingForm = document.querySelector('#time-spending-content .bg-white.rounded-xl');
    if (timeSpendingForm) {
        timeSpendingForm.style.display = 'none';
        console.log('Hidden Time Spending Form');
    } else {
        console.error('Time Spending Form not found');
    }

    // Show subscription plans section that's already in the main page
    const subscriptionPlansSection = document.querySelector('#time-spending-content .mt-8');
    if (subscriptionPlansSection) {
        subscriptionPlansSection.style.display = 'block';
        console.log('Shown subscription plans section');

        // Add a header to the subscription section
        subscriptionPlansSection.innerHTML = `
            <div class="text-center mb-4">
                <div class="inline-flex items-center justify-center w-10 h-10 bg-gradient-to-r from-purple-600 to-indigo-600 rounded-lg mb-2 shadow-sm">
                    <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 3v4M3 5h4M6 17v4m-2-2h4m5-16l2.286 6.857L21 12l-5.714 2.143L13 21l-2.286-6.857L5 12l5.714-2.143L13 3z"></path>
                    </svg>
                </div>
                <h2 class="text-lg font-bold text-gray-900 mb-1">Your Subscription Plan</h2>
                <p class="text-gray-600 text-xs max-w-md mx-auto">Choose a subscription plan to activate your Time Spending service</p>
            </div>

            <div class="bg-white rounded-xl border border-gray-200 shadow-sm">
                <div class="p-4">
                    <!-- Back Button -->
                    <div class="mb-4">
                        <button type="button" onclick="hideInlineSubscriptionPlans()" class="inline-flex items-center px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2 transition-all duration-200">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                            </svg>
                            Back to Time Spending Settings
                        </button>
                    </div>

                    <!-- Subscription Plans Container -->
                    <div id="inline-subscription-plans-container">
                        <div class="text-center py-8">
                            <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                <svg class="w-8 h-8 text-green-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <p class="text-green-800 font-medium">Subscription Plans Section is Working!</p>
                                <p class="text-green-600 text-sm mt-2">Loading subscription plans...</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Smooth scroll to the plans section
        subscriptionPlansSection.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });

        // Load actual plans after a short delay
        setTimeout(function() {
            loadInlineSubscriptionPlans();
        }, 2000);
    } else {
        console.error('Subscription plans section not found');
        alert('Subscription plans section not found in DOM');
    }
}

function hideInlineSubscriptionPlans() {
    console.log('Hiding inline subscription plans...');

    // Hide subscription plans section
    const subscriptionPlansSection = document.querySelector('#time-spending-content .mt-8');
    if (subscriptionPlansSection) {
        subscriptionPlansSection.style.display = 'none';
        console.log('Hidden subscription plans section');
    }

    // Ensure payment modal is hidden
    const paymentModal = document.getElementById('paymentModal');
    if (paymentModal) {
        paymentModal.style.display = 'none';
        paymentModal.classList.add('d-none');
        // Also hide any Bootstrap modal instance
        if (typeof bootstrap !== 'undefined') {
            const modal = bootstrap.Modal.getInstance(paymentModal);
            if (modal) {
                modal.hide();
            }
        }
        console.log('Hidden payment modal');
    }

    // Show Time Spending Service form
    const timeSpendingForm = document.querySelector('#time-spending-content .bg-white.rounded-xl');
    if (timeSpendingForm) {
        timeSpendingForm.style.display = 'block';
        console.log('Shown Time Spending Form');

        // Smooth scroll back to the service section
        timeSpendingForm.scrollIntoView({
            behavior: 'smooth',
            block: 'start'
        });
    }
}

function loadInlineSubscriptionPlans() {
    console.log('Loading inline subscription plans...');

    // Show loading state
    document.getElementById('inline-subscription-plans-container').innerHTML = `
        <div class="text-center py-8">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-600 mx-auto mb-4"></div>
            <p class="text-gray-600">Loading subscription plans...</p>
        </div>
    `;

    // Use fetch to load subscription plans
    fetch('/subscription/plans', {
        method: 'GET',
        headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
        }
    })
    .then(response => response.json())
    .then(response => {
        console.log('Inline plans loaded:', response);
        if (response.success && response.plans && response.plans.length > 0) {
            // Set available plans for inline selection
            availablePlans = response.plans;

            // Check if renderSubscriptionPlans function exists (now that jQuery is available)
            if (typeof renderSubscriptionPlans === 'function') {
                renderSubscriptionPlans(response.plans, '#inline-subscription-plans-container');
            } else {
                // Fallback: render plans manually
                renderPlansManually(response.plans);
            }
        } else {
            document.getElementById('inline-subscription-plans-container').innerHTML = `
                <div class="text-center py-8">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                        <svg class="w-8 h-8 text-blue-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                        <p class="text-blue-800 font-medium">No subscription plans available at the moment.</p>
                    </div>
                </div>
            `;
        }
    })
    .catch(error => {
        console.error('Failed to load subscription plans:', error);
        document.getElementById('inline-subscription-plans-container').innerHTML = `
            <div class="text-center py-8">
                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                    <svg class="w-8 h-8 text-red-600 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    <p class="text-red-800 font-medium">Failed to load subscription plans. Please try again.</p>
                    <button onclick="loadInlineSubscriptionPlans()" class="mt-2 px-3 py-1 bg-red-600 text-white text-sm rounded-lg hover:bg-red-700 transition-colors">
                        Retry
                    </button>
                </div>
            </div>
        `;
    });
}

// Fallback function to render plans manually
function renderPlansManually(plans) {
    let plansHtml = '<div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">';

    plans.forEach(plan => {
        const originalPrice = parseFloat(plan.original_price) || parseFloat(plan.amount) || 0;
        const discountPrice = parseFloat(plan.discount_price) || null;
        const hasDiscount = discountPrice && discountPrice < originalPrice;
        const effectivePrice = hasDiscount ? discountPrice : originalPrice;
        const durationMonths = parseInt(plan.duration_months) || 1;

        plansHtml += `
            <div class="bg-gradient-to-br from-purple-50 to-indigo-100 border border-purple-200 rounded-lg p-4">
                <div class="text-center">
                    <h5 class="text-base font-medium text-gray-900 mb-2">${plan.name || 'Unnamed Plan'}</h5>
                    <p class="text-sm text-gray-600 mb-3">${durationMonths} Month${durationMonths > 1 ? 's' : ''} Plan</p>

                    <div class="mb-4">
                        <div class="text-2xl font-bold text-gray-900">₹${effectivePrice.toLocaleString('en-IN')}</div>
                        <div class="text-sm text-gray-600">₹${(effectivePrice / durationMonths).toFixed(2)}/month</div>
                    </div>

                    <div class="mb-4 text-sm text-gray-700">
                        ${plan.description ? plan.description.replace(/\n/g, '<br>') : `
                            <div>✓ Time Spending Service</div>
                            <div>✓ Enhanced Profile Visibility</div>
                            <div>✓ Priority Support</div>
                        `}
                    </div>

                    <!-- Profile Configuration Notice -->
                    <div class="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg text-xs">
                        <div class="flex items-start gap-2">
                            <svg class="w-4 h-4 text-blue-600 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <div class="text-blue-800">
                                <div class="font-medium mb-1">Automatic Profile Configuration</div>
                                <div class="text-blue-700">
                                    Activating this subscription will automatically:
                                    <ul class="mt-1 ml-3 list-disc">
                                        <li>Make your profile public for maximum visibility</li>
                                        <li>Enable gallery images to showcase yourself</li>
                                    </ul>
                                    <div class="mt-1 text-blue-600">You can modify these settings later in Privacy Settings.</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <button type="button"
                            class="w-full px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 font-medium transition-colors select-plan-btn"
                            data-plan-id="${plan.id || ''}"
                            onclick="selectInlinePlan('${plan.id || ''}')">
                        Activate Plan
                    </button>
                </div>
            </div>
        `;
    });

    plansHtml += '</div>';
    document.getElementById('inline-subscription-plans-container').innerHTML = plansHtml;
}

// Function to wait for libraries to load
function waitForLibraries(callback, maxAttempts = 10) {
    let attempts = 0;

    function checkLibraries() {
        attempts++;

        if (typeof bootstrap !== 'undefined' && typeof Razorpay !== 'undefined' && typeof $ !== 'undefined') {
            callback();
            return;
        }

        if (attempts >= maxAttempts) {
            alert('Error: Required libraries not loaded. Please refresh the page.');
            return;
        }


        setTimeout(checkLibraries, 500);
    }

    checkLibraries();
}

// Function to handle inline plan selection
function selectInlinePlan(planId) {
    console.log('Selecting inline plan:', planId);

    // Wait for libraries to load before proceeding
    waitForLibraries(function() {
        // Find the plan from available plans
        const selectedPlan = availablePlans.find(plan => plan.id == planId);
        if (!selectedPlan) {
            console.error('Plan not found:', planId);
            alert('Error: Selected plan not found. Please try again.');
            return;
        }

        // Use the existing selectPlan function
        selectPlan(planId);
    });
}

document.addEventListener('DOMContentLoaded', function() {
    // Hide the subscription plans section initially
    const subscriptionPlansSection = document.querySelector('#time-spending-content .mt-8');
    if (subscriptionPlansSection) {
        subscriptionPlansSection.style.display = 'none';
        console.log('Initially hidden subscription plans section');
    }

    // Ensure payment modal is hidden on page load
    const paymentModal = document.getElementById('paymentModal');
    if (paymentModal) {
        paymentModal.style.display = 'none';
        paymentModal.classList.add('d-none');
        console.log('Initially hidden payment modal');
    }

    // Initialize time input functionality
    initializeTimeInputs();

    // Handle holiday toggle functionality
    const holidayToggles = document.querySelectorAll('.holiday-toggle');

    holidayToggles.forEach(toggle => {
        toggle.addEventListener('change', function() {
            const day = this.dataset.day;
            const timeInputs = document.getElementById(`time-inputs-${day}`);
            const holidayMessage = document.getElementById(`holiday-message-${day}`);
            const dayRow = document.getElementById(`day-row-${day}`);

            if (this.checked) {
                // Hide time inputs and show holiday message
                timeInputs.style.display = 'none';
                holidayMessage.style.display = 'flex';

                // Add visual feedback to row
                dayRow.classList.add('holiday-active');
            } else {
                // Show time inputs and hide holiday message
                timeInputs.style.display = 'flex';
                holidayMessage.style.display = 'none';

                // Remove visual feedback from row
                dayRow.classList.remove('holiday-active');
            }
        });

        // Set initial state
        const day = toggle.dataset.day;
        const timeInputs = document.getElementById(`time-inputs-${day}`);
        const holidayMessage = document.getElementById(`holiday-message-${day}`);
        const dayRow = document.getElementById(`day-row-${day}`);

        if (toggle.checked) {
            timeInputs.style.display = 'none';
            holidayMessage.style.display = 'flex';
            dayRow.classList.add('holiday-active');
        } else {
            timeInputs.style.display = 'flex';
            holidayMessage.style.display = 'none';
            dayRow.classList.remove('holiday-active');
        }
    });

    // Location autocomplete functionality
    const locationInput = document.getElementById('service_location');
    const locationDropdown = document.getElementById('locationDropdown');
    const locationSuggestions = document.getElementById('locationSuggestions');
    const locationLoading = document.getElementById('locationLoading');
    const locationNoResults = document.getElementById('locationNoResults');
    const locationSelected = document.getElementById('location_selected');
    const locationStatusIndicator = document.getElementById('location-status-indicator');

    let searchTimeout;
    let currentSuggestions = [];
    let selectedIndex = -1;
    let isTyping = false;

    // Initialize status indicator based on current state
    function updateLocationStatus() {
        if (locationSelected.value === '1' && locationInput.value) {
            locationStatusIndicator.classList.remove('hidden');
            locationInput.classList.add('border-green-500');
        } else {
            locationStatusIndicator.classList.add('hidden');
            locationInput.classList.remove('border-green-500');
        }
    }

    // Initial status update
    updateLocationStatus();

    // Function to update Time Spending tab styling in parent window
    function updateTimeSpendingTabStyling() {
        // Check if we're in the profile edit page context
        if (typeof window.updateTimeSpendingTabStyling === 'function') {
            window.updateTimeSpendingTabStyling();
        } else {
            // Try to access parent window function if in iframe/modal context
            try {
                if (window.parent && typeof window.parent.updateTimeSpendingTabStyling === 'function') {
                    window.parent.updateTimeSpendingTabStyling();
                }
            } catch (e) {
                // Silently fail if cross-origin or other access issues
            }
        }
    }

    // Add event listeners to hourly rate field
    const hourlyRateInput = document.getElementById('hourly_rate');
    if (hourlyRateInput) {
        hourlyRateInput.addEventListener('input', function() {
            updateTimeSpendingTabStyling();
        });
        hourlyRateInput.addEventListener('change', function() {
            updateTimeSpendingTabStyling();
        });
    }

    // Function to clear error messages
    function clearLocationError() {
        locationInput.classList.remove('location-validation-error');
        const errorDiv = document.getElementById('location-error-message');
        if (errorDiv) {
            errorDiv.remove();
        }
    }

    // Search locations using OpenStreetMap Nominatim API
    async function searchLocations(query) {
        if (query.length < 3) return [];

        try {
            const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodeURIComponent(query)}&limit=8&countrycodes=in&addressdetails=1`);
            const data = await response.json();

            return data.map(item => {
                const address = item.address || {};
                let displayName = '';

                // Format for Indian locations
                if (address.city || address.town || address.village) {
                    displayName += (address.city || address.town || address.village);
                }

                if (address.state && displayName) {
                    displayName += ', ' + address.state;
                } else if (address.state) {
                    displayName = address.state;
                }

                return {
                    display_name: displayName || item.display_name,
                    full_address: item.display_name
                };
            });
        } catch (error) {
            console.error('Location search error:', error);
            return [];
        }
    }

    function showLocationSuggestions(suggestions) {
        locationLoading.classList.add('hidden');
        locationNoResults.classList.add('hidden');

        if (suggestions.length === 0) {
            locationNoResults.classList.remove('hidden');
            locationDropdown.classList.remove('hidden');
            return;
        }

        locationSuggestions.innerHTML = '';

        suggestions.forEach((suggestion, index) => {
            const suggestionDiv = document.createElement('div');
            suggestionDiv.className = 'location-suggestion px-3 py-2 cursor-pointer text-sm border-b border-gray-100 last:border-b-0';
            suggestionDiv.innerHTML = `
                <div class="font-medium text-gray-900">${suggestion.display_name}</div>
                <div class="text-xs text-gray-500 truncate">${suggestion.full_address}</div>
            `;

            suggestionDiv.addEventListener('click', () => {
                locationInput.value = suggestion.display_name;
                locationSelected.value = '1';

                // Clear any error messages
                clearLocationError();

                // Lock the field after selection
                locationInput.setAttribute('readonly', 'readonly');
                locationInput.placeholder = 'Click to change location selection';

                updateLocationStatus();
                hideLocationDropdown();

                // Update Time Spending tab styling
                updateTimeSpendingTabStyling();
            });

            locationSuggestions.appendChild(suggestionDiv);
        });

        locationDropdown.classList.remove('hidden');
        currentSuggestions = suggestions;
        selectedIndex = -1;
    }

    function hideLocationDropdown() {
        locationDropdown.classList.add('hidden');
        currentSuggestions = [];
        selectedIndex = -1;
    }

    function showLocationLoading() {
        locationSuggestions.innerHTML = '';
        locationNoResults.classList.add('hidden');
        locationLoading.classList.remove('hidden');
        locationDropdown.classList.remove('hidden');
    }

    // Handle location input interactions
    locationInput.addEventListener('click', function(e) {
        enableLocationInput();
    });

    locationInput.addEventListener('focus', function(e) {
        enableLocationInput();
    });

    function enableLocationInput() {
        if (locationInput.hasAttribute('readonly')) {
            locationInput.removeAttribute('readonly');
            locationInput.placeholder = 'Start typing your service location...';
            locationSelected.value = '0';

            // Clear the field to force new selection
            if (locationInput.value) {
                locationInput.value = '';
            }

            clearLocationError();
            updateLocationStatus();
            locationInput.focus();

            // Update Time Spending tab styling when location is cleared
            updateTimeSpendingTabStyling();
        }
    }

    // Input event for autocomplete
    locationInput.addEventListener('input', function(e) {
        const query = e.target.value.trim();
        locationSelected.value = '0'; // Always mark as not selected when typing

        clearTimeout(searchTimeout);

        if (query.length < 3) {
            hideLocationDropdown();
            return;
        }

        showLocationLoading();

        searchTimeout = setTimeout(async () => {
            try {
                const suggestions = await searchLocations(query);
                showLocationSuggestions(suggestions);
            } catch (error) {
                console.error('Search error:', error);
                hideLocationDropdown();
            }
        }, 300);
    });

    // Prevent any manual editing - only allow through dropdown selection
    locationInput.addEventListener('keydown', function(e) {
        // Allow only backspace, delete, and arrow keys for navigation
        const allowedKeys = ['Backspace', 'Delete', 'ArrowUp', 'ArrowDown', 'Enter', 'Escape', 'Tab'];

        if (!allowedKeys.includes(e.key) && !e.ctrlKey && !e.metaKey) {
            // Allow typing only if field is empty or has less than 3 characters
            if (this.value.length >= 3 && locationSelected.value === '0') {
                // Allow normal typing for search
                return;
            }
        }

        // Handle special keys
        if (e.key === 'Backspace' || e.key === 'Delete') {
            locationSelected.value = '0';
        }
    });

    // Hide dropdown when clicking outside
    locationInput.addEventListener('blur', function(e) {
        setTimeout(() => {
            hideLocationDropdown();
        }, 150);
    });

    // Show dropdown when focusing if there are suggestions
    locationInput.addEventListener('focus', function(e) {
        if (currentSuggestions.length > 0) {
            locationDropdown.classList.remove('hidden');
        }
    });

    // Keyboard navigation
    locationInput.addEventListener('keydown', function(e) {
        const suggestions = document.querySelectorAll('.location-suggestion');

        if (locationDropdown.classList.contains('hidden') || suggestions.length === 0) {
            return;
        }

        if (e.key === 'ArrowDown') {
            e.preventDefault();
            selectedIndex = Math.min(selectedIndex + 1, suggestions.length - 1);
            updateSelection(suggestions);
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            selectedIndex = Math.max(selectedIndex - 1, -1);
            updateSelection(suggestions);
        } else if (e.key === 'Enter') {
            e.preventDefault();
            if (selectedIndex >= 0) {
                suggestions[selectedIndex].click();
            }
        } else if (e.key === 'Escape') {
            hideLocationDropdown();
        }
    });

    function updateSelection(suggestions) {
        suggestions.forEach((suggestion, index) => {
            if (index === selectedIndex) {
                suggestion.classList.add('selected');
            } else {
                suggestion.classList.remove('selected');
            }
        });
    }

    // Form validation to ensure location is selected from dropdown
    const form = document.querySelector('form');
    if (form) {
        form.addEventListener('submit', function(e) {
            console.log('Form submission - Location selected value:', locationSelected.value);
            console.log('Form submission - Location input value:', locationInput.value);

            // Check if location is properly selected from dropdown
            if (!locationInput.value || locationSelected.value !== '1') {
                e.preventDefault();
                e.stopPropagation();

                // Add visual error styling
                locationInput.classList.add('location-validation-error');

                // Show error message in proper container
                const errorContainer = document.getElementById('location-error-container');
                let errorDiv = document.getElementById('location-error-message');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.id = 'location-error-message';
                    errorDiv.className = 'text-sm text-red-600 font-medium bg-red-50 border border-red-200 rounded-lg p-3';
                    errorContainer.appendChild(errorDiv);
                }
                errorDiv.textContent = 'Please select a location from the dropdown suggestions. Manual typing is not allowed.';

                // Show alert as well
                alert('Please select a location from the dropdown suggestions. Manual typing is not allowed.');

                // Clear the invalid input
                locationInput.value = '';
                locationSelected.value = '0';
                updateLocationStatus();

                // Scroll to the field and focus
                locationInput.scrollIntoView({ behavior: 'smooth', block: 'center' });
                setTimeout(() => {
                    enableLocationInput();
                }, 500);

                return false;
            } else {
                // Remove error styling if validation passes
                clearLocationError();
                console.log('Form validation passed, submitting...');
            }
        });
    }
    // Subscription overlay functionality - COMPLETELY DISABLED
    // No automatic popup will show

    // Check subscription status periodically
    setInterval(function() {
        checkSubscriptionStatus();
    }, 60000); // Check every minute

    function showSubscriptionOverlay() {
        // Disable all form fields
        const formFields = document.querySelectorAll('#time-spending-form input, #time-spending-form select, #time-spending-form textarea, #time-spending-form button');
        formFields.forEach(field => {
            field.disabled = true;
        });

        // Show subscription modal with native Bootstrap
        const subscriptionModal = document.getElementById('subscriptionModal');
        if (subscriptionModal && typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(subscriptionModal);
            modal.show();
        }
    }

    function hideSubscriptionOverlay() {
        // Re-enable all form fields
        const formFields = document.querySelectorAll('#time-spending-form input, #time-spending-form select, #time-spending-form textarea, #time-spending-form button');
        formFields.forEach(field => {
            field.disabled = false;
        });

        // Hide subscription modal with native Bootstrap
        const subscriptionModal = document.getElementById('subscriptionModal');
        if (subscriptionModal && typeof bootstrap !== 'undefined') {
            const modal = bootstrap.Modal.getInstance(subscriptionModal);
            if (modal) {
                modal.hide();
            }
        }
    }

    // Update plan button functionality with native JavaScript
    const updatePlanBtn = document.getElementById('updatePlanBtn');
    if (updatePlanBtn) {
        updatePlanBtn.addEventListener('click', function() {
            showInlineSubscriptionPlans();
        });
    }

    function checkSubscriptionStatus() {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch('/subscription/status', {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                const hasActive = data.has_active_subscription;
                const currentlyActive = @json($user->hasActiveTimeSpendingSubscription() ?? false);

                // If subscription status changed, reload the page
                if (hasActive !== currentlyActive) {
                    window.location.reload();
                }
            }
        })
        .catch(error => {
            // Silently fail - don't show errors for background checks
        });
    }
});
</script>

<!-- Payment Processing Modal -->
<div class="modal fade" id="paymentModal" tabindex="-1" aria-labelledby="paymentModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="paymentModalLabel">Processing Payment</h5>
            </div>
            <div class="modal-body text-center py-4">
                <div class="spinner-border text-primary mb-3" role="status">
                    <span class="visually-hidden">Processing...</span>
                </div>
                <p class="mb-0">Please wait while we process your payment...</p>
            </div>
        </div>
    </div>
</div>

<!-- Add jQuery CDN for subscription functionality -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://checkout.razorpay.com/v1/checkout.js"></script>
<script>
// Embed subscription plans data directly in the page with safe handling
let SUBSCRIPTION_PLANS = [];
let availablePlans = []; // Global variable for inline plans
let selectedPlan = null; // Global variable for selected plan

try {
    const plansData = @json(\App\Models\SubscriptionPlan::getActivePlans());
    SUBSCRIPTION_PLANS = Array.isArray(plansData) ? plansData : [];
    availablePlans = SUBSCRIPTION_PLANS; // Initialize with embedded data
} catch (e) {
    console.warn('Failed to load subscription plans data:', e);
    SUBSCRIPTION_PLANS = [];
    availablePlans = [];
}

document.addEventListener('DOMContentLoaded', function() {
    // Variables are now declared globally above

    // Error handling wrapper
    function safeExecute(fn, context = null, ...args) {
        try {
            return fn.apply(context, args);
        } catch (error) {
            console.error('Safe execution failed:', error);
            return null;
        }
    }

    // Load subscription plans when modal is shown
    const subscriptionModal = document.getElementById('subscriptionModal');
    if (subscriptionModal) {
        subscriptionModal.addEventListener('shown.bs.modal', function() {
            safeExecute(() => {
                console.log('Modal shown, loading plans...');
                loadSubscriptionPlans();
            });
        });
    }

    // Test function removed for production

    function loadSubscriptionPlans() {
        console.log('Loading subscription plans...');

        // Show loading state
        const loadingPlans = document.getElementById('loadingPlans');
        const plansContent = document.getElementById('plansContent');
        const plansError = document.getElementById('plansError');

        if (loadingPlans) loadingPlans.style.display = 'block';
        if (plansContent) plansContent.style.display = 'none';
        if (plansError) plansError.style.display = 'none';

        // Try to use embedded plans data first
        if (SUBSCRIPTION_PLANS && SUBSCRIPTION_PLANS.length > 0) {
            console.log('Using embedded plans data:', SUBSCRIPTION_PLANS);
            setTimeout(function() {
                if (loadingPlans) loadingPlans.style.display = 'none';
                availablePlans = SUBSCRIPTION_PLANS;
                renderPlans(SUBSCRIPTION_PLANS);
                if (plansContent) plansContent.style.display = 'block';
            }, 500); // Small delay to show loading state
            return;
        }

        // Fallback to Fetch API if embedded data is not available
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch('/subscription/plans?v=' + Date.now(), {
            method: 'GET',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Plans response:', data);
            if (loadingPlans) loadingPlans.style.display = 'none';

            if (data.success && data.plans && data.plans.length > 0) {
                availablePlans = data.plans;
                renderPlans(data.plans);
                if (plansContent) plansContent.style.display = 'block';
            } else {
                console.error('Failed to load plans:', data);
                if (plansError) plansError.style.display = 'block';
            }
        })
        .catch(error => {
            console.error('Fetch error loading plans:', error);
            console.log('Falling back to embedded data...');
            if (loadingPlans) loadingPlans.style.display = 'none';

            // Try embedded data as final fallback
            if (SUBSCRIPTION_PLANS && SUBSCRIPTION_PLANS.length > 0) {
                availablePlans = SUBSCRIPTION_PLANS;
                renderPlans(SUBSCRIPTION_PLANS);
                if (plansContent) plansContent.style.display = 'block';
            } else {
                if (plansError) plansError.style.display = 'block';
            }
        });
    }

    function renderPlans(plans) {
        try {
            // Ensure plans is an array and has valid data
            const plansContentElement = document.getElementById('plansContent');
            if (!Array.isArray(plans) || plans.length === 0) {
                if (plansContentElement) {
                    plansContentElement.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-exclamation-triangle text-warning" style="font-size: 2rem;"></i>
                            <p class="mt-2 text-muted">No subscription plans available at the moment.</p>
                        </div>
                    `;
                }
                return;
            }

            let plansHtml = '<div class="row g-3">';

            // Use for loop instead of forEach to avoid iterator issues
            for (let i = 0; i < plans.length; i++) {
                const plan = plans[i];
                if (!plan || typeof plan !== 'object') {
                    console.warn('Invalid plan data at index', i, plan);
                    continue;
                }
                const isPopular = plan.duration_months === 3; // Mark 3-month plan as popular

                // Safe data extraction with defaults
                const originalPrice = parseFloat(plan.original_price) || parseFloat(plan.amount) || 0;
                const discountPrice = parseFloat(plan.discount_price) || null;
                const hasDiscount = discountPrice && discountPrice < originalPrice;
                const effectivePrice = hasDiscount ? discountPrice : originalPrice;
                const discountPercentage = hasDiscount ? Math.round(((originalPrice - discountPrice) / originalPrice) * 100) : 0;
                const savingsAmount = hasDiscount ? (originalPrice - discountPrice) : 0;
                const durationMonths = parseInt(plan.duration_months) || 1;

                plansHtml += `
                    <div class="col-md-6">
                        <div class="card h-100 plan-card ${isPopular ? 'border-primary' : ''}" data-plan-id="${plan.id || ''}">
                            ${isPopular ? '<div class="badge bg-primary position-absolute top-0 start-50 translate-middle px-3 py-1">Most Popular</div>' : ''}
                            <div class="card-body text-center p-4">
                                <h5 class="card-title fw-bold">${plan.name || 'Unnamed Plan'}</h5>
                                <div class="pricing-section mb-3">
                                    ${hasDiscount ? `
                                        <div class="d-flex justify-content-center align-items-center gap-2 mb-2">
                                            <span class="display-6 fw-bold text-primary">₹${effectivePrice.toFixed(2)}</span>
                                            <span class="text-muted text-decoration-line-through fs-5">₹${originalPrice.toFixed(2)}</span>
                                        </div>
                                        <div class="badge bg-success mb-2 fw-bold">
                                            <i class="fas fa-tag me-1"></i>${discountPercentage}% OFF - Save ₹${savingsAmount.toFixed(2)}
                                        </div>
                                    ` : `
                                        <div class="display-6 fw-bold text-primary mb-2">₹${effectivePrice.toFixed(2)}</div>
                                    `}
                                </div>
                                <p class="text-muted mb-3">₹${(effectivePrice / durationMonths).toFixed(2)}/month</p>
                                ${plan.description ? `<p class="text-sm text-muted mb-3">${plan.description}</p>` : ''}
                                <button type="button" class="btn btn-primary w-100 select-plan-btn" data-plan-id="${plan.id || ''}">
                                    Select Plan
                                </button>
                            </div>
                        </div>
                    </div>
                `;
            }

            plansHtml += '</div>';
            if (plansContentElement) {
                plansContentElement.innerHTML = plansHtml;

                // Add click handlers for plan selection with native JavaScript
                const selectButtons = plansContentElement.querySelectorAll('.select-plan-btn');
                selectButtons.forEach(button => {
                    button.addEventListener('click', function() {
                        try {
                            const planId = this.getAttribute('data-plan-id');
                            if (planId) {
                                selectPlan(planId);
                            } else {
                                console.error('No plan ID found for button');
                            }
                        } catch (e) {
                            console.error('Error in plan selection:', e);
                        }
                    });
                });
            }
        } catch (error) {
            console.error('Error rendering plans:', error);
            const plansContentElement = document.getElementById('plansContent');
            if (plansContentElement) {
                plansContentElement.innerHTML = `
                    <div class="text-center py-4">
                        <i class="bi bi-exclamation-triangle text-danger" style="font-size: 2rem;"></i>
                        <p class="mt-2 text-muted">Error loading subscription plans. Please refresh the page.</p>
                    </div>
                `;
            }
        }
    }

    function selectPlan(planId) {
        // Check if required libraries are loaded
        if (typeof bootstrap === 'undefined') {
            console.error('Bootstrap is not loaded');
            alert('Error: Required libraries not loaded. Please refresh the page.');
            return;
        }

        if (typeof Razorpay === 'undefined') {
            console.error('Razorpay is not loaded');
            alert('Error: Payment gateway not loaded. Please refresh the page.');
            return;
        }

        if (typeof $ === 'undefined') {
            console.error('jQuery is not loaded');
            alert('Error: Required libraries not loaded. Please refresh the page.');
            return;
        }

        selectedPlan = availablePlans.find(plan => plan.id === planId);
        if (!selectedPlan) {
            console.error('Plan not found:', planId);
            alert('Error: Selected plan not found. Please try again.');
            return;
        }

        // Show payment modal with native Bootstrap
        const paymentModal = document.getElementById('paymentModal');
        if (paymentModal && typeof bootstrap !== 'undefined') {
            const modal = new bootstrap.Modal(paymentModal);
            modal.show();
        }

        // Initiate purchase with fetch API
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch('/subscription/purchase', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ plan_id: planId })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                if (data.payment_required && data.razorpay_amount > 0) {
                    // Open Razorpay payment
                    openRazorpayPayment(data);
                } else {
                    // Payment completed with wallet only - show success modal
                    showWalletPaymentSuccess(data);
                }
            } else {
                hidePaymentModal();
                showError(data.message || 'Failed to initiate purchase.');
            }
        })
        .catch(error => {
            hidePaymentModal();
            console.error('Purchase initiation error:', error);
            showError('Error initiating purchase. Please try again.');
        });
    }

    function openRazorpayPayment(paymentData) {
        const options = {
            key: paymentData.razorpay_key,
            amount: Math.round(paymentData.razorpay_amount * 100), // Amount in paise
            currency: "INR",
            name: "SettingWala",
            description: `Time Spending Subscription - ${paymentData.plan.name}`,
            image: "{{ asset('images/logo.png') }}",
            order_id: paymentData.razorpay_order_id,
            handler: function (response) {
                processPayment(
                    paymentData.plan.id,
                    response.razorpay_payment_id,
                    response.razorpay_order_id,
                    response.razorpay_signature
                );
            },
            prefill: {
                name: "{{ Auth::user()->name }}",
                email: "{{ Auth::user()->email }}",
                contact: "{{ Auth::user()->contact_number ?? '' }}"
            },
            theme: {
                color: "#EC4899"
            },
            modal: {
                ondismiss: function() {
                    hidePaymentModal();
                }
            }
        };

        hidePaymentModal();
        const rzp = new Razorpay(options);
        rzp.open();
    }

    function processPayment(planId, paymentId, orderId, signature) {
        showPaymentModal();

        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        fetch('/subscription/process-payment', {
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': csrfToken,
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                plan_id: planId,
                razorpay_payment_id: paymentId,
                razorpay_order_id: orderId,
                razorpay_signature: signature
            })
        })
        .then(response => response.json())
        .then(data => {
            hidePaymentModal();

            if (data.success) {
                hideSubscriptionModal();
                showSuccess(data.message);

                // Reload the page to show updated subscription status
                setTimeout(function() {
                    window.location.reload();
                }, 2000);
            } else {
                showError(data.message || 'Payment processing failed.');
            }
        })
        .catch(error => {
            hidePaymentModal();
            console.error('Payment processing error:', error);
            showError('Payment processing failed. Please try again.');
        });
    }

    // Helper functions for modal management
    function showPaymentModal() {
        const paymentModal = document.getElementById('paymentModal');

        if (paymentModal && typeof bootstrap !== 'undefined') {
            paymentModal.style.display = 'block';
            paymentModal.classList.remove('d-none');
            paymentModal.classList.add('show', 'modal');
            const modal = new bootstrap.Modal(paymentModal);
            modal.show();
        }
    }

    function hidePaymentModal() {
        const paymentModal = document.getElementById('paymentModal');
        if (paymentModal) {
            paymentModal.style.display = 'none';
            paymentModal.classList.add('d-none');
            paymentModal.classList.remove('show');
            if (typeof bootstrap !== 'undefined') {
                const modal = bootstrap.Modal.getInstance(paymentModal);
                if (modal) {
                    modal.hide();
                }
            }
        }
    }

    function showWalletPaymentSuccess(data) {
        // Update payment modal to show success message
        const paymentModal = document.getElementById('paymentModal');
        const modalTitle = document.getElementById('paymentModalLabel');
        const modalBody = paymentModal?.querySelector('.modal-body');

        if (modalTitle) {
            modalTitle.textContent = 'Payment Successful!';
        }

        if (modalBody) {
            modalBody.innerHTML = `
                <div class="text-center py-4">
                    <div class="text-success mb-3">
                        <i class="fas fa-check-circle" style="font-size: 3rem;"></i>
                    </div>
                    <h5 class="text-success mb-3">Subscription Activated!</h5>
                    <p class="mb-3">Your subscription has been successfully activated using your wallet balance.</p>
                    <p class="text-muted small">Amount: ₹${data.wallet_usage || data.plan?.amount || '0'}</p>
                    <button type="button" class="btn btn-success mt-3" onclick="location.reload()">
                        Continue
                    </button>
                </div>
            `;
        }

        // Keep the modal visible to show success
        showPaymentModal();

        // Auto-reload after 5 seconds
        setTimeout(() => {
            location.reload();
        }, 5000);
    }

    function hideSubscriptionModal() {
        const subscriptionModal = document.getElementById('subscriptionModal');
        if (subscriptionModal && typeof bootstrap !== 'undefined') {
            const modal = bootstrap.Modal.getInstance(subscriptionModal);
            if (modal) {
                modal.hide();
            }
        }
    }

    function showSuccess(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-success alert-dismissible fade show';
        alertDiv.setAttribute('role', 'alert');
        alertDiv.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.insertBefore(alertDiv, document.body.firstChild);

        setTimeout(function() {
            alertDiv.style.transition = 'opacity 0.5s ease';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 500);
        }, 5000);
    }

    function showError(message) {
        const alertDiv = document.createElement('div');
        alertDiv.className = 'alert alert-danger alert-dismissible fade show';
        alertDiv.setAttribute('role', 'alert');
        alertDiv.innerHTML = `
            <i class="bi bi-exclamation-triangle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        document.body.insertBefore(alertDiv, document.body.firstChild);

        setTimeout(function() {
            alertDiv.style.transition = 'opacity 0.5s ease';
            alertDiv.style.opacity = '0';
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, 500);
        }, 5000);
    }

    // Function to update earnings breakdown when hourly rate changes
    function updateEarningsBreakdown() {
        const hourlyRateInput = document.getElementById('hourly_rate');
        const earningsBreakdown = document.getElementById('earnings-breakdown');
        const displayHourlyRate = document.getElementById('display-hourly-rate');
        const commissionAmount = document.getElementById('commission-amount');
        const netEarnings = document.getElementById('net-earnings');

        if (!hourlyRateInput || !earningsBreakdown) return;

        const hourlyRate = parseFloat(hourlyRateInput.value) || 0;
        const commissionPercentage = {{ \App\Models\Setting::get('commission_percentage', 10) }};

        // Show breakdown only if hourly rate > 0 AND commission percentage > 0
        if (hourlyRate > 0 && commissionPercentage > 0) {
            const commission = (hourlyRate * commissionPercentage) / 100;
            const netAmount = hourlyRate - commission;

            // Update display values
            displayHourlyRate.textContent = '₹' + hourlyRate.toFixed(2);
            commissionAmount.textContent = '-₹' + commission.toFixed(2);
            netEarnings.textContent = '₹' + netAmount.toFixed(2);

            // Show breakdown
            earningsBreakdown.style.display = 'block';
        } else {
            // Hide breakdown if no rate set or commission is 0%
            earningsBreakdown.style.display = 'none';
        }
    }

    // Initialize breakdown on page load
    updateEarningsBreakdown();
});

// Make function globally available
window.updateEarningsBreakdown = function() {
    const hourlyRateInput = document.getElementById('hourly_rate');
    const earningsBreakdown = document.getElementById('earnings-breakdown');
    const displayHourlyRate = document.getElementById('display-hourly-rate');
    const commissionAmount = document.getElementById('commission-amount');
    const netEarnings = document.getElementById('net-earnings');

    if (!hourlyRateInput || !earningsBreakdown) return;

    const hourlyRate = parseFloat(hourlyRateInput.value) || 0;
    const commissionPercentage = {{ \App\Models\Setting::get('commission_percentage', 10) }};

    // Show breakdown only if hourly rate > 0 AND commission percentage > 0
    if (hourlyRate > 0 && commissionPercentage > 0) {
        const commission = (hourlyRate * commissionPercentage) / 100;
        const netAmount = hourlyRate - commission;

        // Update display values
        displayHourlyRate.textContent = '₹' + hourlyRate.toFixed(2);
        commissionAmount.textContent = '-₹' + commission.toFixed(2);
        netEarnings.textContent = '₹' + netAmount.toFixed(2);

        // Show breakdown
        earningsBreakdown.style.display = 'block';
    } else {
        // Hide breakdown if no rate set or commission is 0%
        earningsBreakdown.style.display = 'none';
    }
};
</script>

<style>
.plan-card {
    cursor: pointer;
    transition: all 0.3s ease;
}

.plan-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.plan-card.border-primary {
    position: relative;
}

/* Payment modal visibility control */
#paymentModal {
    display: none;
}

#paymentModal.show {
    display: block !important;
}

/* Override Bootstrap modal display when showing */
#paymentModal.modal.show {
    display: block !important;
}
</style>
